2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: bb87e1ba-1708-4243-b768-7e8b6fc661e9 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'status': 'failed', 'result': 'Exception in workflow \'8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a\': Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field \'main_input\' not found in parameters"', 'workflow_status': 'failed', 'error': 'Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field \'main_input\' not found in parameters"', 'error_type': 'Exception'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Workflow '8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a' final status: failed, result: Exception in workflow '8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a': Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
Exception: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
2025-06-29 16:04:29	
    raise result
2025-06-29 16:04:29	
  File "/app/app/core_/executor_core.py", line 267, in execute
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/core_/executor_core.py", line 285, in execute
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/core_/executor_core.py", line 341, in execute
2025-06-29 16:04:29	
                        ^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    execution_success = await execution_task
2025-06-29 16:04:29	
  File "/app/app/execution/executor_server_kafka.py", line 330, in handle_workflow_result
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
During handling of the above exception, another exception occurred:
2025-06-29 16:04:29	
2025-06-29 16:04:29	
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
    raise Exception(f"Tool execution error: {error_message}")
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await self._execute_standard_or_reflection_transition(transition)
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
During handling of the above exception, another exception occurred:
2025-06-29 16:04:29	
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 361, in execute_tool
2025-06-29 16:04:29	
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    execution_result = await executor.execute_tool(
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 16:04:29	
Exception: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
2025-06-29 16:04:29	
    raise result
2025-06-29 16:04:29	
  File "/app/app/core_/executor_core.py", line 267, in execute
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/core_/executor_core.py", line 285, in execute
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
During handling of the above exception, another exception occurred:
2025-06-29 16:04:29	
2025-06-29 16:04:29	
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
    raise Exception(f"Tool execution error: {error_message}")
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await self._execute_standard_or_reflection_transition(transition)
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
During handling of the above exception, another exception occurred:
2025-06-29 16:04:29	
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 361, in execute_tool
2025-06-29 16:04:29	
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    execution_result = await executor.execute_tool(
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 16:04:29	
Exception: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
2025-06-29 16:04:29	
    raise result
2025-06-29 16:04:29	
  File "/app/app/core_/executor_core.py", line 267, in execute
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
During handling of the above exception, another exception occurred:
2025-06-29 16:04:29	
2025-06-29 16:04:29	
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
    raise Exception(f"Tool execution error: {error_message}")
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await self._execute_standard_or_reflection_transition(transition)
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
During handling of the above exception, another exception occurred:
2025-06-29 16:04:29	
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 361, in execute_tool
2025-06-29 16:04:29	
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    execution_result = await executor.execute_tool(
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-CombineTextComponent-1751005393963: NoneType: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-CombineTextComponent-1751005393963: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-CombineTextComponent-1751005393963: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field \'main_input\' not found in parameters"'), []]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'result': 'Completed transition in 0.10 seconds', 'message': 'Transition completed in 0.10 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 40, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 40, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 0.10 seconds
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'loop_iteration_6', 'loop_iteration_4', 'loop_iteration_1', 'loop_iteration_2', 'loop_iteration_0', 'loop_iteration_3', 'transition-LoopNode-*************', 'current_iteration', 'transition-MergeDataComponent-*************', 'loop_iteration_5'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': []}, 'status': 'completed', 'timestamp': 1751193269.886231}}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': [], 'status': 'completed', 'sequence': 39, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 39, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Execution result from Components executor: []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - INFO - Result received for request 9d46c489-6e63-4d3a-bbf5-40ea26d01f6d.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-CombineTextComponent-1751005393963', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 38, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 38, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 361, in execute_tool
2025-06-29 16:04:29	
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    execution_result = await executor.execute_tool(
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-1751005393963': Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - ERROR - Error during node execution 12952205-fbdd-44b4-a501-a253f8217a37: Node execution failed: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Received valid result for request_id 9d46c489-6e63-4d3a-bbf5-40ea26d01f6d
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Result consumer received message: Offset=1056
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - WARNING - Received error response for request_id 12952205-fbdd-44b4-a501-a253f8217a37: Error combining text for request_id 12952205-fbdd-44b4-a501-a253f8217a37: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Result consumer received message: Offset=1055
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 9d46c489-6e63-4d3a-bbf5-40ea26d01f6d...
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Request 9d46c489-6e63-4d3a-bbf5-40ea26d01f6d sent successfully using provided producer.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': '9d46c489-6e63-4d3a-bbf5-40ea26d01f6d', 'correlation_id': 'bb87e1ba-1708-4243-b768-7e8b6fc661e9', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Added correlation_id bb87e1ba-1708-4243-b768-7e8b6fc661e9 to payload
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 9d46c489-6e63-4d3a-bbf5-40ea26d01f6d) using provided producer.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 37, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 37, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - tool Parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 36, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 36, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 12952205-fbdd-44b4-a501-a253f8217a37...
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Request 12952205-fbdd-44b4-a501-a253f8217a37 sent successfully using provided producer.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '12952205-fbdd-44b4-a501-a253f8217a37', 'correlation_id': 'bb87e1ba-1708-4243-b768-7e8b6fc661e9', 'transition_id': 'transition-CombineTextComponent-1751005393963'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-1751005393963 to payload
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Added correlation_id bb87e1ba-1708-4243-b768-7e8b6fc661e9 to payload
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 12952205-fbdd-44b4-a501-a253f8217a37) using provided producer.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-CombineTextComponent-1751005393963', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 35, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 35, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-1751005393963' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1751005393963
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1751005393963' (type=standard, execution_type=Components)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1751005393963', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1751005393963', 'status': 'started', 'sequence': 34, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 34, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1751005393963
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - ==============================
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Workflow paused: False
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Workflow status: inactive
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Workflow status: inactive
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Results stored for 11 transitions
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Completed transitions (11): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'loop_iteration_3', 'loop_iteration_4', 'loop_iteration_5', 'loop_iteration_6', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Waiting transitions (0): []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Pending transitions (0): []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Terminated: False
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Cleared 2 pending transitions: {'transition-CombineTextComponent-1751005393963', 'transition-MergeDataComponent-*************'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Updated waiting=set(), pending={'transition-CombineTextComponent-1751005393963', 'transition-MergeDataComponent-*************'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Moved transitions from waiting to pending: {'transition-CombineTextComponent-1751005393963'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Transition transition-CombineTextComponent-1751005393963 is now ready (dependencies met: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************'])
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-1751005393963'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Workflow state saved to Redis for workflow ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Set key 'workflow_state:bb87e1ba-1708-4243-b768-7e8b6fc661e9' with TTL of 600 seconds
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:bb87e1ba-1708-4243-b768-7e8b6fc661e9'
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-MergeDataComponent-*************']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'result': 'Completed transition in 0.64 seconds', 'message': 'Transition completed in 0.64 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 33, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 33, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 0.64 seconds
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-1751005393963' - should have been executed internally by loop executor
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-1751005393963', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-1751005393963main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-1751005393963']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 0, 'end': 6}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-1751005393963'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-1751005393963']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-1751005393963 (has current_item/iteration indicators)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-1751005393963'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'loop_iteration_6', 'loop_iteration_4', 'loop_iteration_1', 'loop_iteration_2', 'loop_iteration_0', 'loop_iteration_3', 'transition-LoopNode-*************', 'current_iteration', 'loop_iteration_5'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': []}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': []}, 'status': 'completed', 'sequence': 32, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 32, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
}
2025-06-29 16:04:29	
  "final_results": []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Checking execution result for errors: {
2025-06-29 16:04:29	
}
2025-06-29 16:04:29	
  "final_results": []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Execution result from loop executor: {
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - INFO - ✅ Loop node execution completed for transition: transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 7}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 31, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 31, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - INFO - 📊 Loop execution completed with metadata: {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 7}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - INFO - 🔄 NEW AGGREGATION METHOD RETURNED: []
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 🔍 Sample extracted data: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 🔍 Extracted 0 data items from iterations
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 🔄 Aggregating 0 iteration results using aggregation_type: collect_all
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - INFO - 🔄 CALLING NEW AGGREGATION METHOD with 0 iteration results
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - ERROR - ❌ Iteration 7 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-CombineTextComponent-1751005393963', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 30, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 30, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 361, in execute_tool
2025-06-29 16:04:29	
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    execution_result = await executor.execute_tool(
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-1751005393963': Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - ERROR - Error during node execution 24248a4d-1047-49b6-a59d-78268852b23d: Node execution failed: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - WARNING - Received error response for request_id 24248a4d-1047-49b6-a59d-78268852b23d: Error combining text for request_id 24248a4d-1047-49b6-a59d-78268852b23d: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Result consumer received message: Offset=1054
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 24248a4d-1047-49b6-a59d-78268852b23d...
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Request 24248a4d-1047-49b6-a59d-78268852b23d sent successfully using provided producer.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '24248a4d-1047-49b6-a59d-78268852b23d', 'correlation_id': 'bb87e1ba-1708-4243-b768-7e8b6fc661e9', 'transition_id': 'transition-CombineTextComponent-1751005393963'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-1751005393963 to payload
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Added correlation_id bb87e1ba-1708-4243-b768-7e8b6fc661e9 to payload
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 24248a4d-1047-49b6-a59d-78268852b23d) using provided producer.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-CombineTextComponent-1751005393963', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 29, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 29, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-1751005393963' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-1751005393963
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-1751005393963' (type=standard, execution_type=Components)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-1751005393963', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-1751005393963', 'status': 'started', 'sequence': 28, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 28, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-1751005393963
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-1751005393963
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-1751005393963
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_6
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 6, 'iteration_index': 6, 'iteration_metadata': {'timestamp': 2205750.251196996, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 7}}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-1751005393963'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'loop_iteration_6', 'loop_iteration_4', 'loop_iteration_1', 'loop_iteration_2', 'loop_iteration_0', 'loop_iteration_3', 'current_iteration', 'loop_iteration_5'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 6
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-1751005393963'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'loop_iteration_6', 'loop_iteration_4', 'loop_iteration_1', 'loop_iteration_2', 'loop_iteration_0', 'loop_iteration_3', 'current_iteration', 'loop_iteration_5'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - INFO - Marked transition loop_iteration_6 as completed (was_pending=False, was_waiting=False)
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition loop_iteration_6 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - RedisManager - DEBUG - Set key 'result:loop_iteration_6' with TTL of 900 seconds
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_6
2025-06-29 16:04:29	
2025-06-29 10:34:29 - StateManager - DEBUG - Stored result for transition loop_iteration_6 in memory: 6
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - DEBUG - 🔄 Executing iteration 7 chain directly
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - INFO - 🔄 Starting iteration 7/7
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - ERROR - ❌ Iteration 6 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-1751005393963: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: bb87e1ba-1708-4243-b768-7e8b6fc661e9, response: {'transition_id': 'transition-CombineTextComponent-1751005393963', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 27, 'workflow_status': 'running'}
2025-06-29 16:04:29	
2025-06-29 10:34:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 27, corr_id bb87e1ba-1708-4243-b768-7e8b6fc661e9):
2025-06-29 16:04:29	
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
    raise e
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 361, in execute_tool
2025-06-29 16:04:29	
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-06-29 16:04:29	
    execution_result = await executor.execute_tool(
2025-06-29 16:04:29	
  File "/app/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
2025-06-29 16:04:29	
2025-06-29 10:34:29 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-1751005393963': Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
2025-06-29 16:04:29	
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
             ^^^^^^^^^^^^
2025-06-29 16:04:29	
    result = await future
2025-06-29 16:04:29	
  File "/app/app/services/node_executor.py", line 344, in execute_tool
2025-06-29 16:04:29	
Traceback (most recent call last):
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - ERROR - Error during node execution bc087a2b-7273-4f15-8d25-951a07046921: Node execution failed: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - WARNING - Received error response for request_id bc087a2b-7273-4f15-8d25-951a07046921: Error combining text for request_id bc087a2b-7273-4f15-8d25-951a07046921: "Required field 'main_input' not found in parameters"
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Result consumer received message: Offset=1053
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Waiting indefinitely for result for request bc087a2b-7273-4f15-8d25-951a07046921...
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Request bc087a2b-7273-4f15-8d25-951a07046921 sent successfully using provided producer.
2025-06-29 16:04:29	
2025-06-29 10:34:29 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'bc087a2b-7273-4f15-8d25-951a07046921', 'correlation_id': 'bb87e1ba-1708-4243-b768-7e8b6fc661e9', 'transition_id': 'transition-CombineTextComponent-1751005393963'}