2025-06-29 14:52:45 - Main - INFO - Starting Server
2025-06-29 14:52:45 - Main - INFO - Connection at: **************:9092
2025-06-29 14:52:45 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-29 14:52:45 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-29 14:52:45 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-29 14:52:45 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-29 14:52:45 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 14:52:47 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 14:52:47 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 14:52:48 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 14:52:50 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-29 14:52:50 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-29 14:52:53 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 14:52:53 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-29 14:52:53 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 14:52:55 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 14:52:55 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 14:52:56 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 14:52:56 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-29 14:52:56 - RedisEventListener - INFO - Redis event listener started
2025-06-29 14:52:56 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-29 14:52:56 - StateManager - DEBUG - Using provided database connections
2025-06-29 14:52:56 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 14:52:56 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 14:52:56 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 14:52:57 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-29 14:52:57 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 14:52:57 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 14:52:57 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-29 14:52:57 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-29 14:52:57 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-29 14:52:57 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-29 14:53:00 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-29 14:53:00 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-29 14:53:00 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-29 14:53:15 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-29 14:53:21 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-29 14:53:21 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-29 14:53:21 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-29 14:53:27 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-29 14:53:27 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-29 14:53:27 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-29 14:53:34 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-29 14:53:34 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-29 14:53:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:53:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:53:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:53:54 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:54:02 - AgentExecutor - DEBUG - Result consumer received message: Offset=345243
2025-06-29 14:54:02 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 300ca5c3-391d-4080-930d-1c9b0ed4df98
2025-06-29 14:54:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:54:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:54:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:54:54 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f', 'data': b'expired'}
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f'
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Extracted key: workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 14:54:57 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:54:57 - RedisEventListener - INFO - Archiving workflow state for workflow: ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:55:01 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 14:55:02 - PostgresManager - DEBUG - Updated workflow state for correlation_id: temp_initialization
2025-06-29 14:55:02 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: temp_initialization
2025-06-29 14:55:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:55:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:55:55 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:55:55 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:56:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:56:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:56:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:56:54 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:57:01 - AgentExecutor - DEBUG - Result consumer received message: Offset=345244
2025-06-29 14:57:01 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 4370ab71-6212-4fee-98c2-3c4a6210079c
2025-06-29 14:57:02 - AgentExecutor - DEBUG - Result consumer received message: Offset=345245
2025-06-29 14:57:02 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: a8d3daec-c87d-45b3-b60b-e91c4baeccf9
2025-06-29 14:57:03 - AgentExecutor - DEBUG - Result consumer received message: Offset=345246
2025-06-29 14:57:03 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: be3f4533-950a-4ed2-ac40-5849a706c70b
2025-06-29 14:57:05 - AgentExecutor - DEBUG - Result consumer received message: Offset=345247
2025-06-29 14:57:05 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 0eb45d78-9c90-434b-b942-3e1a8e32c05d
2025-06-29 14:57:05 - AgentExecutor - DEBUG - Result consumer received message: Offset=345248
2025-06-29 14:57:05 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 4879a618-8e3c-4e61-a8b0-48b145614e07
2025-06-29 14:57:07 - AgentExecutor - DEBUG - Result consumer received message: Offset=345249
2025-06-29 14:57:07 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 7d6aab9d-23ea-4a91-ad18-fc18bf9e0f2f
2025-06-29 14:57:08 - AgentExecutor - DEBUG - Result consumer received message: Offset=345250
2025-06-29 14:57:08 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 5c2b0712-83b0-47c4-80d7-d8727fad8f02
2025-06-29 14:57:10 - AgentExecutor - DEBUG - Result consumer received message: Offset=345251
2025-06-29 14:57:10 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 50bbb553-c404-400e-b64f-5df379add71e
2025-06-29 14:57:11 - AgentExecutor - DEBUG - Result consumer received message: Offset=345252
2025-06-29 14:57:11 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 3c4033aa-c89c-47bd-ae20-f3adf30a0121
2025-06-29 14:57:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:57:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:57:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:57:54 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:58:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:58:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:58:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:58:54 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:59:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:59:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:59:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:59:54 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae', 'data': b'expired'}
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae'
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Extracted key: workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 15:00:52 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:52 - RedisEventListener - INFO - Archiving workflow state for workflow: 81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:56 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 15:00:56 - PostgresManager - DEBUG - Updated workflow state for correlation_id: temp_initialization
2025-06-29 15:00:57 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: temp_initialization
2025-06-29 15:00:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:00:58 - RedisEventListener - WARNING - Failed to send keep-alive PING to Redis: Bad response from PING health check
2025-06-29 15:00:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:00:59 - RedisEventListener - DEBUG - Keep-alive status: results=False, state=True
2025-06-29 15:01:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:01:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:01:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:01:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:02:54 - AgentExecutor - DEBUG - Result consumer received message: Offset=***********-06-29 15:02:54 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: f9dc7503-1ace-4aa2-b0e6-462105cf0aef
2025-06-29 15:02:55 - AgentExecutor - DEBUG - Result consumer received message: Offset=345254
2025-06-29 15:02:55 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 040766ef-fbd3-42f3-8384-01f75d170d0f
2025-06-29 15:02:56 - AgentExecutor - DEBUG - Result consumer received message: Offset=345255
2025-06-29 15:02:56 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: b2d7726d-8815-4e9e-b4ff-a2923633a564
2025-06-29 15:02:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:02:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:02:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:02:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:02:58 - AgentExecutor - DEBUG - Result consumer received message: Offset=345256
2025-06-29 15:02:58 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: b477f640-3e30-4446-b23d-8ae2d3ed1641
2025-06-29 15:03:00 - AgentExecutor - DEBUG - Result consumer received message: Offset=345257
2025-06-29 15:03:00 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: c7eda384-d5de-4df3-9091-128f5ccc16c4
2025-06-29 15:03:00 - AgentExecutor - DEBUG - Result consumer received message: Offset=345258
2025-06-29 15:03:00 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: ea7b03a1-1c55-4337-9f25-a69c7765264e
2025-06-29 15:03:01 - AgentExecutor - DEBUG - Result consumer received message: Offset=345259
2025-06-29 15:03:01 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'eac6f902-7015-4a61-9cf8-d5ec8c1d4c11', 'session_id': 'eac6f902-7015-4a61-9cf8-d5ec8c1d4c11', 'event_type': 'error', 'agent_response': {}, 'success': False, 'message': 'Request validation failed', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': None, 'request_id': None, 'error_code': 'ERR_1001', 'details': {'original_error': "1 validation error for AgentMessageRequest\nagent_config.model_config.model\n  Field required [type=missing, input_value={'model_provider': 'opena...147-b43c-c190a99da7b9'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", 'request_id': 'eac6f902-7015-4a61-9cf8-d5ec8c1d4c11', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'timestamp': '2025-06-29T09:33:00.598258Z'}}
2025-06-29 15:03:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:03:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:03:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:03:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405', 'data': b'expired'}
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405'
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Extracted key: workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 15:03:58 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:03:58 - RedisEventListener - INFO - Archiving workflow state for workflow: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:04:02 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 15:04:02 - PostgresManager - DEBUG - Updated workflow state for correlation_id: temp_initialization
2025-06-29 15:04:03 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: temp_initialization
2025-06-29 15:04:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:04:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:04:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:04:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:05:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:05:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:05:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:05:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:06:51 - AgentExecutor - DEBUG - Result consumer received message: Offset=345260
2025-06-29 15:06:51 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: d01c6004-abeb-4a5d-b29c-39c4ae51a1d5
2025-06-29 15:06:53 - AgentExecutor - DEBUG - Result consumer received message: Offset=345261
2025-06-29 15:06:53 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 052b6755-6b7f-4198-a60e-6d998e4973af
2025-06-29 15:06:55 - AgentExecutor - DEBUG - Result consumer received message: Offset=345262
2025-06-29 15:06:55 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: a61bdf8a-1520-4029-8013-f7323e29abc5
2025-06-29 15:06:56 - AgentExecutor - DEBUG - Result consumer received message: Offset=345263
2025-06-29 15:06:56 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 1ff57a56-b7ff-492c-aa82-909fa67baa6c
2025-06-29 15:06:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:06:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:06:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:06:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:06:58 - AgentExecutor - DEBUG - Result consumer received message: Offset=345264
2025-06-29 15:06:58 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 552e1937-0bf8-479c-9bad-ae7b9b234e7d
2025-06-29 15:06:59 - AgentExecutor - DEBUG - Result consumer received message: Offset=345265
2025-06-29 15:06:59 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 2eaf7ea1-49bd-4f4e-ad83-a77d3f505ec9
2025-06-29 15:07:00 - AgentExecutor - DEBUG - Result consumer received message: Offset=345266
2025-06-29 15:07:00 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 310f4ded-42a6-4077-9267-d426b4cddd62
2025-06-29 15:07:01 - AgentExecutor - DEBUG - Result consumer received message: Offset=345267
2025-06-29 15:07:01 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: bd47135a-4ae6-4a51-9402-61835fb52b96
2025-06-29 15:07:03 - AgentExecutor - DEBUG - Result consumer received message: Offset=345268
2025-06-29 15:07:03 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 39250f43-5ab9-4408-bfc7-b295824df016
2025-06-29 15:07:04 - AgentExecutor - DEBUG - Result consumer received message: Offset=345269
2025-06-29 15:07:04 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 45a5fc6a-22dd-4403-8fe4-f3505070778f
2025-06-29 15:07:05 - AgentExecutor - DEBUG - Result consumer received message: Offset=345270
2025-06-29 15:07:05 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 48d8f426-291f-4ca2-ab52-80b4a39cf9fc
2025-06-29 15:07:06 - AgentExecutor - DEBUG - Result consumer received message: Offset=345271
2025-06-29 15:07:06 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 114451ef-79d6-43f0-bfd7-84402b720076
2025-06-29 15:07:07 - AgentExecutor - DEBUG - Result consumer received message: Offset=345272
2025-06-29 15:07:07 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 44db276d-9c13-4fc8-bbc2-96464f4c20a7
2025-06-29 15:07:09 - AgentExecutor - DEBUG - Result consumer received message: Offset=345273
2025-06-29 15:07:09 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 3515b3d9-4e65-445c-af60-20f71ea5df0c
2025-06-29 15:07:10 - AgentExecutor - DEBUG - Result consumer received message: Offset=345274
2025-06-29 15:07:10 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: d314f739-6547-4fc4-a9ad-165fcd4173cc
2025-06-29 15:07:12 - AgentExecutor - DEBUG - Result consumer received message: Offset=345275
2025-06-29 15:07:12 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 9c6de033-f393-4d63-ac0c-451bc3984f92
2025-06-29 15:07:13 - AgentExecutor - DEBUG - Result consumer received message: Offset=345276
2025-06-29 15:07:13 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: b64cf4d8-8675-4e9f-aa09-40b3ee01bfcb
2025-06-29 15:07:14 - AgentExecutor - DEBUG - Result consumer received message: Offset=345277
2025-06-29 15:07:14 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: fb9963cf-c837-4073-836f-d33d45e774bf
2025-06-29 15:07:15 - AgentExecutor - DEBUG - Result consumer received message: Offset=345278
2025-06-29 15:07:15 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 0d8fe986-7c3d-4846-84bb-f648e3da1f89
2025-06-29 15:07:17 - AgentExecutor - DEBUG - Result consumer received message: Offset=345279
2025-06-29 15:07:17 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 0d5403ec-d57f-48ea-a14f-c1579a75f583
2025-06-29 15:07:18 - AgentExecutor - DEBUG - Result consumer received message: Offset=345280
2025-06-29 15:07:18 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 102f6334-d289-4182-ad7e-1de463d62d17
2025-06-29 15:07:21 - AgentExecutor - DEBUG - Result consumer received message: Offset=345281
2025-06-29 15:07:21 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 2ba97aed-db89-4bef-b59e-74872c605d5b
2025-06-29 15:07:22 - AgentExecutor - DEBUG - Result consumer received message: Offset=345282
2025-06-29 15:07:22 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 0d138d00-9da4-496b-991e-37a87aca56bd
2025-06-29 15:07:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=345283
2025-06-29 15:07:23 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 54572fce-8975-46b0-83ea-cbd0ca44d269
2025-06-29 15:07:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:07:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:07:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:07:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:08:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:08:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:08:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:08:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:09:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:09:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:09:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:09:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:10:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:10:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:10:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:10:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:11:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:11:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:11:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:11:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:12:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:12:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:12:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:12:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:13:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:13:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:13:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:13:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:14:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:14:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:14:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:14:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:15:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:15:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:15:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:15:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:15:58 - AgentExecutor - DEBUG - Result consumer received message: Offset=345284
2025-06-29 15:15:58 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 1321a013-053f-463a-a4ef-3293d887654e
2025-06-29 15:15:58 - AgentExecutor - DEBUG - Result consumer received message: Offset=345285
2025-06-29 15:15:58 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 268d6cd5-51e4-4164-bcce-1f0d09456fa8
2025-06-29 15:16:04 - AgentExecutor - DEBUG - Result consumer received message: Offset=345286
2025-06-29 15:16:04 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 47a3c8f8-a6de-49d8-ba11-e1aeccc797b0
2025-06-29 15:16:09 - AgentExecutor - DEBUG - Result consumer received message: Offset=345287
2025-06-29 15:16:09 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: f6d259de-9f34-416c-88d1-0ac749077d23
2025-06-29 15:16:15 - AgentExecutor - DEBUG - Result consumer received message: Offset=345288
2025-06-29 15:16:15 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 81b5fa9f-2a69-448e-845f-5bb2f6783cf4
2025-06-29 15:16:22 - AgentExecutor - DEBUG - Result consumer received message: Offset=345289
2025-06-29 15:16:22 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 3aee1158-afaa-4d4d-9513-fd01052bd092
2025-06-29 15:16:30 - AgentExecutor - DEBUG - Result consumer received message: Offset=345290
2025-06-29 15:16:30 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 11224925-5150-440e-8771-a42246d8845b
2025-06-29 15:16:37 - AgentExecutor - DEBUG - Result consumer received message: Offset=345291
2025-06-29 15:16:37 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: f3972394-8a3b-4b86-a776-a27876da5c43
2025-06-29 15:16:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:16:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:16:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:16:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:17:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:17:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:17:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:17:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:18:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:18:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:18:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:18:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:19:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:19:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:19:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:19:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:20:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:20:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:20:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:20:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:21:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:21:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:21:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:21:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:22:21 - AgentExecutor - DEBUG - Result consumer received message: Offset=345292
2025-06-29 15:22:21 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 0b5b21b8-1e50-4124-86f4-82382c2e3c74
2025-06-29 15:22:21 - AgentExecutor - DEBUG - Result consumer received message: Offset=345293
2025-06-29 15:22:21 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 845714c6-9cf5-49f4-9429-9187d6172aad
2025-06-29 15:22:26 - AgentExecutor - DEBUG - Result consumer received message: Offset=345294
2025-06-29 15:22:26 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: a18189e8-d0dd-4138-b64d-76f1cdcdbebf
2025-06-29 15:22:30 - AgentExecutor - DEBUG - Result consumer received message: Offset=345295
2025-06-29 15:22:30 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 4ea02d18-98d3-4d2e-b504-497cff0051b8
2025-06-29 15:22:34 - AgentExecutor - DEBUG - Result consumer received message: Offset=345296
2025-06-29 15:22:34 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: f8fc4636-19b2-480a-b0b0-ef4a10dc3073
2025-06-29 15:22:39 - AgentExecutor - DEBUG - Result consumer received message: Offset=345297
2025-06-29 15:22:39 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: c66b9e2b-f709-4433-b233-e84bfbcf64dc
2025-06-29 15:22:45 - AgentExecutor - DEBUG - Result consumer received message: Offset=345298
2025-06-29 15:22:45 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 3493a5be-0514-40e8-aeac-67b8245568b3
2025-06-29 15:22:52 - AgentExecutor - DEBUG - Result consumer received message: Offset=345299
2025-06-29 15:22:52 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: d6036d7e-394a-4923-b23c-41c4d9155f8c
2025-06-29 15:22:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:22:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:22:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:22:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:23:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:23:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:23:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:23:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:24:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:24:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:24:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:24:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:25:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:25:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:25:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:25:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:26:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:26:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:26:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:26:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:27:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:27:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:27:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:27:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:28:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:28:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:28:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:28:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:29:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:29:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:29:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:29:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:30:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:30:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:30:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:30:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
Consumer - INFO - Sent response to workflow-responses with correlation ID: 260898bf-6d5e-4a48-b8ef-55d195c98405, response: {'result': 'Starting execution of transition: transition-AgenticAI-1751005983601', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-1751005983601', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-29 14:53:58 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751005983601' (type=initial, execution_type=agent)
2025-06-29 14:53:58 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-29 14:53:58 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751005983601
2025-06-29 14:53:58 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-29 14:53:58 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-29 14:53:58 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-29 14:53:58 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-06-29 14:53:58 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-29 14:53:58 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-06-29 14:53:58 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-29 14:53:58 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751005983601' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-29 14:53:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 260898bf-6d5e-4a48-b8ef-55d195c98405):
2025-06-29 14:53:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 260898bf-6d5e-4a48-b8ef-55d195c98405, response: {'transition_id': 'transition-AgenticAI-1751005983601', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-29 14:53:58 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 300ca5c3-391d-4080-930d-1c9b0ed4df98) with correlation_id: 260898bf-6d5e-4a48-b8ef-55d195c98405, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-29 14:53:58 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-29 14:53:58 - AgentExecutor - DEBUG - Added correlation_id 260898bf-6d5e-4a48-b8ef-55d195c98405 to payload
2025-06-29 14:53:58 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '300ca5c3-391d-4080-930d-1c9b0ed4df98', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '260898bf-6d5e-4a48-b8ef-55d195c98405', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': '15ac5aef-029a-4d53-b28e-9f9ea91a686e', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-29 14:53:58 - AgentExecutor - DEBUG - Request 300ca5c3-391d-4080-930d-1c9b0ed4df98 sent successfully using dedicated agent producer to kafka.ruh.ai:9094.
2025-06-29 14:53:58 - AgentExecutor - DEBUG - Waiting for single response result for request 300ca5c3-391d-4080-930d-1c9b0ed4df98...
2025-06-29 14:53:58 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1055, corr_id: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 14:54:00 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:54:01 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:54:01 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:54:01 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f', 'data': b'expired'}
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f'
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Extracted key: workflow_state:ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 14:54:57 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 14:54:57 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:54:57 - RedisEventListener - INFO - Archiving workflow state for workflow: ce4061e1-a438-43ac-bb20-c0874f2ae53f
2025-06-29 14:55:01 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 14:55:02 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 14:55:02 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 14:55:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:55:04 - RedisEventListener - WARNING - Failed to send keep-alive PING to Redis: Bad response from PING health check
2025-06-29 14:55:04 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:55:04 - RedisEventListener - DEBUG - Keep-alive status: results=False, state=True
2025-06-29 14:56:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:56:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:56:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:56:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:57:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:57:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:57:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:57:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:58:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:58:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:58:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:58:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 14:59:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 14:59:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:59:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 14:59:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:00:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:00:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:00:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:00:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae', 'data': b'expired'}
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae'
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Extracted key: workflow_state:81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 15:00:52 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 15:00:52 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:52 - RedisEventListener - INFO - Archiving workflow state for workflow: 81727fe6-a855-49e2-9bf2-835a35f46eae
2025-06-29 15:00:56 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 15:00:56 - PostgresManager - DEBUG - Updated workflow state for correlation_id: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:00:57 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:01:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:01:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:01:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:01:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:02:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:02:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:02:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:02:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:03:02 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:03:02 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:03:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:03:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405', 'data': b'expired'}
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405'
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Extracted key: workflow_state:260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 15:03:58 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 15:03:58 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:03:58 - RedisEventListener - INFO - Archiving workflow state for workflow: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:04:02 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 15:04:02 - PostgresManager - DEBUG - Updated workflow state for correlation_id: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:04:03 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 260898bf-6d5e-4a48-b8ef-55d195c98405
2025-06-29 15:04:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:04:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:04:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:04:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:05:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:05:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:05:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:05:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:06:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:06:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:06:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:06:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:07:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:07:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:07:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:07:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:08:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:08:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:08:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:08:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:09:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:09:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:09:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:09:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:10:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:10:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:10:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:10:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:11:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:11:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:11:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:11:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:12:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:12:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:12:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:12:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:13:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:13:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:13:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:13:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:14:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:14:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:14:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:14:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:15:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:15:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:15:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:15:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:16:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:16:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:16:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:16:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:17:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:17:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:17:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:17:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:18:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:18:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:18:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:18:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:19:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:19:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:19:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:19:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:20:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:20:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:20:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:20:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:21:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:21:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:21:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:21:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:22:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:22:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:22:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:22:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:23:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:23:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:23:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:23:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:24:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:24:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:24:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:24:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:25:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:25:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:25:04 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:25:04 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:26:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:26:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:26:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:26:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:27:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:27:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:27:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:27:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:28:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:28:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:28:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:28:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:29:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:29:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:29:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:29:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:30:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:30:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:30:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:30:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 15:31:03 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 15:31:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:31:03 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 15:31:03 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
